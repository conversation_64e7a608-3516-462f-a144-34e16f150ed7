package com.sandu.xinye.common.kit;

import com.jfinal.kit.StrKit;

/**
 * 推广码工具类
 * 提供推广码格式验证、标准化等功能
 * 
 * <AUTHOR> Team
 * @since 2024-07-30
 */
public class PromotionCodeKit {
    
    /**
     * 推广码格式正则表达式：6-8位字母数字组合
     */
    private static final String PROMOTION_CODE_PATTERN = "^[A-Za-z0-9]{6,8}$";
    
    /**
     * 验证推广码格式是否正确
     * 
     * @param promotionCode 推广码
     * @return true-格式正确，false-格式错误
     */
    public static boolean isValidFormat(String promotionCode) {
        if (StrKit.isBlank(promotionCode)) {
            return false;
        }
        
        // 去除首尾空格后验证
        String trimmedCode = promotionCode.trim();
        return trimmedCode.matches(PROMOTION_CODE_PATTERN);
    }
    
    /**
     * 标准化推广码
     * 去除首尾空格并转换为大写
     * 
     * @param promotionCode 原始推广码
     * @return 标准化后的推广码，如果输入为空则返回null
     */
    public static String normalize(String promotionCode) {
        if (StrKit.isBlank(promotionCode)) {
            return null;
        }
        
        return promotionCode.trim().toUpperCase();
    }
    
    /**
     * 验证并标准化推广码
     * 
     * @param promotionCode 原始推广码
     * @return 标准化后的推广码，如果格式错误则返回null
     */
    public static String validateAndNormalize(String promotionCode) {
        if (!isValidFormat(promotionCode)) {
            return null;
        }
        
        return normalize(promotionCode);
    }
    
    /**
     * 检查推广码是否为空或无效
     * 
     * @param promotionCode 推广码
     * @return true-为空或无效，false-有效
     */
    public static boolean isBlankOrInvalid(String promotionCode) {
        return StrKit.isBlank(promotionCode) || !isValidFormat(promotionCode);
    }
    
    /**
     * 获取推广码格式说明
     * 
     * @return 推广码格式说明文本
     */
    public static String getFormatDescription() {
        return "推广码格式：6-8位字母数字组合";
    }
    
    /**
     * 生成推广码格式错误的提示信息
     * 
     * @return 错误提示信息
     */
    public static String getFormatErrorMessage() {
        return "推广码格式错误，" + getFormatDescription();
    }
}
