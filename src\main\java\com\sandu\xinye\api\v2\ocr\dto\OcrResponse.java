package com.sandu.xinye.api.v2.ocr.dto;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * OCR识别响应数据传输对象 - 简化中间态结构
 * 用于在后端识别结果和APP端XPrinter格式之间进行数据转换
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
public class OcrResponse {
    
    /**
     * 图片信息
     */
    private Map<String, Object> imageInfo;
    
    /**
     * 识别到的元素列表
     */
    private List<Map<String, Object>> elements;
    
    public OcrResponse() {
        this.imageInfo = new HashMap<>();
        this.elements = new ArrayList<>();
    }
    
    /**
     * 设置图片信息
     */
    public void setImageInfo(int width, int height, String format) {
        this.imageInfo.put("width", width);
        this.imageInfo.put("height", height);
        this.imageInfo.put("format", format);
    }
    
    /**
     * 添加文本元素 - 简化中间态结构
     */
    public void addTextElement(int x, int y, int width, int height, 
                              String content, boolean bold, boolean italic) {
        // 使用简单算法作为默认值
        double charWidth = content != null && content.length() > 0 ? 
                          (double) width / content.length() : 0.0;
        addTextElement(x, y, width, height, content, bold, italic, charWidth);
    }
    
    /**
     * 添加文本元素 - 带智能charWidth计算
     */
    public void addTextElement(int x, int y, int width, int height, 
                              String content, boolean bold, boolean italic, double charWidth) {
        // 使用默认值调用完整版本
        addTextElement(x, y, width, height, content, bold, italic, charWidth, 0.0, "horizontal", 1.0, false);
    }
    
    /**
     * 添加文本元素 - 完整版本，包含所有属性
     */
    public void addTextElement(int x, int y, int width, int height, 
                              String content, boolean bold, boolean italic, double charWidth,
                              double rotationAngle, String textDirection, double confidence, boolean isHandwritten) {
        Map<String, Object> element = new HashMap<>();
        element.put("elementType", "1");
        element.put("x", x);
        element.put("y", y);
        element.put("width", width);
        element.put("height", height);
        element.put("content", content);
        element.put("bold", bold);
        element.put("italic", italic);
        element.put("charWidth", Math.round(charWidth * 10.0) / 10.0); // 保留1位小数
        
        // 新增属性
        element.put("rotationAngle", Math.round(rotationAngle * 10.0) / 10.0); // 保留1位小数
        element.put("textDirection", textDirection);
        element.put("confidence", Math.round(confidence * 1000.0) / 1000.0); // 保留3位小数
        element.put("isHandwritten", isHandwritten);
        
        this.elements.add(element);
    }
    
    /**
     * 添加条形码元素 - 简化中间态结构
     */
    public void addBarcodeElement(int x, int y, int width, int height,
                                 String content, String barcodeType) {
        Map<String, Object> element = new HashMap<>();
        element.put("elementType", "2");
        element.put("x", x);
        element.put("y", y);
        element.put("width", width);
        element.put("height", height);
        element.put("content", content);
        element.put("barcodeType", convertBarcodeType(barcodeType));
        
        this.elements.add(element);
    }
    
    /**
     * 添加二维码元素 - 简化中间态结构
     */
    public void addQrCodeElement(int x, int y, int width, int height, String content) {
        Map<String, Object> element = new HashMap<>();
        element.put("elementType", "7");
        element.put("x", x);
        element.put("y", y);
        element.put("width", width);
        element.put("height", height);
        element.put("content", content);
        
        this.elements.add(element);
    }
    
    /**
     * 添加表格元素 - 简化中间态结构
     */
    public void addTableElement(int x, int y, int width, int height,
                               int rows, int cols, List<Map<String, Object>> cells) {
        addTableElement(x, y, width, height, rows, cols, cells, null, null);
    }
    
    /**
     * 添加表格元素 - 完整版本，包含行高列宽信息
     */
    public void addTableElement(int x, int y, int width, int height,
                               int rows, int cols, List<Map<String, Object>> cells,
                               List<Integer> rowHeights, List<Integer> columnWidths) {
        Map<String, Object> element = new HashMap<>();
        element.put("elementType", "10");
        element.put("x", x);
        element.put("y", y);
        element.put("width", width);
        element.put("height", height);
        element.put("rowCount", rows);
        element.put("colCount", cols);
        element.put("cells", cells);
        
        // 添加行高和列宽信息
        if (rowHeights != null && !rowHeights.isEmpty()) {
            element.put("rowHeights", rowHeights);
        }
        if (columnWidths != null && !columnWidths.isEmpty()) {
            element.put("columnWidths", columnWidths);
        }
        
        this.elements.add(element);
    }
    
    /**
     * 添加图片元素 - 简化中间态结构
     */
    public void addImageElement(int x, int y, int width, int height,
                               String imageUrl, String imageType, 
                               double confidence, boolean isEmbedded) {
        Map<String, Object> element = new HashMap<>();
        element.put("elementType", "8");
        element.put("x", x);
        element.put("y", y);
        element.put("width", width);
        element.put("height", height);
        element.put("imageUrl", imageUrl);
        element.put("imageType", imageType);
        element.put("confidence", Math.round(confidence * 1000.0) / 1000.0); // 保留3位小数
        element.put("isEmbedded", isEmbedded);
        
        this.elements.add(element);
    }
    
    /**
     * 添加边框元素 - 简化中间态结构
     * 边框是特殊的表格：1行1列且内容为空
     */
    public void addBorderElement(int x, int y, int width, int height) {
        Map<String, Object> element = new HashMap<>();
        element.put("elementType", "11"); // 11 表示边框元素
        element.put("x", x);
        element.put("y", y);
        element.put("width", width);
        element.put("height", height);
        
        this.elements.add(element);
    }
    
    /**
     * 转换条码类型编号为标准字符串
     */
    private String convertBarcodeType(String typeCode) {
        if (typeCode == null) {
            return "CODE_128";
        }
        
        switch (typeCode) {
            case "1": return "CODE_39";
            case "2": return "CODE_93";
            case "3": return "CODE_11";
            case "4": return "CODE_128";
            case "5": return "EAN_8";
            case "6": return "EAN_13";
            case "7": return "UPC_A";
            case "8": return "UPC_E";
            case "9": return "CODABAR";
            case "10": return "ITF";
            default: return "CODE_128";
        }
    }
    
    // Getters and Setters
    public Map<String, Object> getImageInfo() {
        return imageInfo;
    }
    
    public void setImageInfo(Map<String, Object> imageInfo) {
        this.imageInfo = imageInfo;
    }
    
    public List<Map<String, Object>> getElements() {
        return elements;
    }
    
    public void setElements(List<Map<String, Object>> elements) {
        this.elements = elements;
    }
}
