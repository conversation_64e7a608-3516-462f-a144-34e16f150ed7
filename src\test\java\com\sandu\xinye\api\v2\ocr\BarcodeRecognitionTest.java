package com.sandu.xinye.api.v2.ocr;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sandu.xinye.api.v2.ocr.dto.OcrResponse;
import com.sandu.xinye.api.v2.ocr.dto.TextInResponse;
import org.junit.Test;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;

/**
 * 条码识别测试
 * 测试ZXing对TextIn提供的条码图片的识别能力
 */
public class BarcodeRecognitionTest {

    @Test
    public void testBarcodeRecognitionFromTextInUrl() throws IOException {
        // 读取跨境物流测试数据
        String jsonContent = new String(Files.readAllBytes(Paths.get("data/跨境物流-TextIn识别返回.json")));
        JSONObject jsonResponse = JSON.parseObject(jsonContent);
        
        // 创建TextInResponse对象
        TextInResponse textinResponse = new TextInResponse();
        textinResponse.setResult(jsonResponse);
        
        // 创建ElementConverter实例
        ElementConverter converter = new ElementConverter();
        
        // 创建一个虚拟的图片文件用于测试
        File testImageFile = new File("test.jpg");
        
        // 转换数据
        OcrResponse response = converter.convertToXPrinterFormat(textinResponse, testImageFile, 840, 600);
        
        // 验证转换结果
        assertNotNull("转换结果不应为空", response);
        
        // 查找条码元素
        List<Map<String, Object>> elements = response.getElements();
        assertNotNull("元素列表不应为空", elements);
        assertFalse("应该包含元素", elements.isEmpty());
        
        boolean foundBarcode = false;
        for (Map<String, Object> element : elements) {
            String elementType = (String) element.get("elementType");
            // elementType "2" 表示条形码
            if ("2".equals(elementType)) {
                foundBarcode = true;
                
                String content = (String) element.get("content");
                
                System.out.println("找到条码元素:");
                System.out.println("  内容: '" + content + "'");
                System.out.println("  完整元素信息: " + element);
                
                // 验证条码内容不为空
                assertNotNull("条码内容不应为空", content);
                assertFalse("条码内容不应为空字符串", content.trim().isEmpty());
                
                // 验证条码内容是数字（根据日志显示应该是"123456789012"）
                assertTrue("条码内容应该是数字", content.matches("\\d+"));
                assertEquals("条码内容应该是123456789012", "123456789012", content);
                
                break;
            }
        }
        
        assertTrue("应该找到条码元素", foundBarcode);
    }
}
